import 'package:flutter/services.dart';
import 'package:sqflite/sqflite.dart';

/// Manages database migrations using SQL files as assets
class MigrationManager {
  static const String _migrationsAssetPath = 'packages/core/lib/migrations';

  /// List of available migration files (hardcoded for now)
  static const List<String> _migrationFiles = [
    '001_initial_schema.sql',
    '002_add_indexes.sql',
    '003_add_metadata_table.sql',
  ];
  
  /// Get all migration files sorted by version
  static List<String> _getMigrationFiles() {
    // Return the hardcoded list of migration files
    final files = List<String>.from(_migrationFiles);

    // Sort files by their numeric prefix (001_, 002_, etc.)
    files.sort((a, b) {
      final aVersion = _extractVersionFromFilename(a);
      final bVersion = _extractVersionFromFilename(b);
      return aVersion.compareTo(bVersion);
    });

    return files;
  }
  
  /// Extract version number from migration filename
  static int _extractVersionFromFilename(String filename) {
    final match = RegExp(r'^(\d+)_').firstMatch(filename);
    if (match == null) {
      throw FormatException('Invalid migration filename format: $filename. '
          'Expected format: 001_description.sql');
    }
    return int.parse(match.group(1)!);
  }
  
  /// Read SQL content from migration file asset
  static Future<String> _readMigrationFile(String filename) async {
    try {
      final assetPath = '$_migrationsAssetPath/$filename';
      return await rootBundle.loadString(assetPath);
    } catch (e) {
      throw StateError('Migration file not found: $filename. Error: $e');
    }
  }
  
  /// Get the latest migration version
  static int getLatestVersion() {
    final files = _getMigrationFiles();
    if (files.isEmpty) {
      return 0;
    }
    
    return _extractVersionFromFilename(files.last);
  }
  
  /// Execute migrations for database creation (onCreate)
  static Future<void> runMigrationsOnCreate(Database db, int version) async {
    print('Running migrations for database creation (version $version)');
    
    final files = _getMigrationFiles();
    
    for (final filename in files) {
      final fileVersion = _extractVersionFromFilename(filename);
      
      // Only run migrations up to the target version
      if (fileVersion <= version) {
        print('Executing migration: $filename');
        final sql = await _readMigrationFile(filename);
        await _executeSqlStatements(db, sql, filename);
      }
    }
    
    print('Database creation migrations completed');
  }
  
  /// Execute migrations for database upgrade (onUpgrade)
  static Future<void> runMigrationsOnUpgrade(
      Database db, int oldVersion, int newVersion) async {
    print('Running migrations for database upgrade: $oldVersion -> $newVersion');
    
    final files = _getMigrationFiles();
    
    for (final filename in files) {
      final fileVersion = _extractVersionFromFilename(filename);
      
      // Only run migrations that are newer than old version and up to new version
      if (fileVersion > oldVersion && fileVersion <= newVersion) {
        print('Executing migration: $filename');
        final sql = await _readMigrationFile(filename);
        await _executeSqlStatements(db, sql, filename);
      }
    }
    
    print('Database upgrade migrations completed');
  }
  
  /// Execute SQL statements from a migration file
  static Future<void> _executeSqlStatements(
      Database db, String sql, String filename) async {
    try {
      // Remove comments and split SQL by semicolons
      final cleanedSql = _removeComments(sql);
      final statements = cleanedSql
          .split(';')
          .map((s) => s.trim())
          .where((s) => s.isNotEmpty)
          .toList();

      print('Executing ${statements.length} statements from $filename');

      for (int i = 0; i < statements.length; i++) {
        final statement = statements[i];
        if (statement.isNotEmpty) {
          print('Statement ${i + 1}: ${statement.substring(0, statement.length > 50 ? 50 : statement.length)}...');
          await db.execute(statement);
        }
      }
    } catch (e) {
      throw Exception('Error executing migration $filename: $e');
    }
  }

  /// Remove SQL comments from a string
  static String _removeComments(String sql) {
    final lines = sql.split('\n');
    final cleanedLines = <String>[];

    for (final line in lines) {
      final trimmedLine = line.trim();
      // Skip comment lines and empty lines
      if (!trimmedLine.startsWith('--') && trimmedLine.isNotEmpty) {
        cleanedLines.add(line);
      }
    }

    return cleanedLines.join('\n');
  }
  
  /// Validate migration files (check for proper naming and syntax)
  static void validateMigrations() {
    final files = _getMigrationFiles();

    if (files.isEmpty) {
      throw StateError('No migration files found');
    }

    // Check for sequential numbering
    for (int i = 0; i < files.length; i++) {
      final expectedVersion = i + 1;
      final actualVersion = _extractVersionFromFilename(files[i]);

      if (actualVersion != expectedVersion) {
        throw StateError('Migration files must be numbered sequentially. '
            'Expected version $expectedVersion, found $actualVersion in ${files[i]}');
      }
    }

    print('Migration validation passed: ${files.length} files found');
  }
  
  /// Get list of available migrations with their versions
  static List<Map<String, dynamic>> getMigrationInfo() {
    final files = _getMigrationFiles();
    
    return files.map((filename) {
      final version = _extractVersionFromFilename(filename);
      final description = filename
          .replaceFirst(RegExp(r'^\d+_'), '')
          .replaceFirst('.sql', '')
          .replaceAll('_', ' ');
      
      return {
        'version': version,
        'filename': filename,
        'description': description,
      };
    }).toList();
  }
}
