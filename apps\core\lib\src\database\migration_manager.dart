import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as path;

/// Manages database migrations using SQL files
class MigrationManager {
  static String? _migrationsPath;

  /// Set the migrations path (should be called during initialization)
  static void setMigrationsPath(String path) {
    _migrationsPath = path;
  }

  /// Get the migrations path
  static String get migrationsPath {
    if (_migrationsPath == null) {
      throw StateError('Migrations path not set. Call setMigrationsPath() first.');
    }
    return _migrationsPath!;
  }
  
  /// Get all migration files sorted by version
  static List<String> _getMigrationFiles() {
    final migrationsDir = Directory(migrationsPath);

    if (!migrationsDir.existsSync()) {
      throw StateError('Migrations directory not found: $migrationsPath');
    }
    
    final files = migrationsDir
        .listSync()
        .whereType<File>()
        .where((file) => file.path.endsWith('.sql'))
        .map((file) => path.basename(file.path))
        .toList();
    
    // Sort files by their numeric prefix (001_, 002_, etc.)
    files.sort((a, b) {
      final aVersion = _extractVersionFromFilename(a);
      final bVersion = _extractVersionFromFilename(b);
      return aVersion.compareTo(bVersion);
    });
    
    return files;
  }
  
  /// Extract version number from migration filename
  static int _extractVersionFromFilename(String filename) {
    final match = RegExp(r'^(\d+)_').firstMatch(filename);
    if (match == null) {
      throw FormatException('Invalid migration filename format: $filename. '
          'Expected format: 001_description.sql');
    }
    return int.parse(match.group(1)!);
  }
  
  /// Read SQL content from migration file
  static String _readMigrationFile(String filename) {
    final file = File(path.join(migrationsPath, filename));
    
    if (!file.existsSync()) {
      throw StateError('Migration file not found: ${file.path}');
    }
    
    return file.readAsStringSync();
  }
  
  /// Get the latest migration version
  static int getLatestVersion() {
    final files = _getMigrationFiles();
    if (files.isEmpty) {
      return 0;
    }
    
    return _extractVersionFromFilename(files.last);
  }
  
  /// Execute migrations for database creation (onCreate)
  static Future<void> runMigrationsOnCreate(Database db, int version) async {
    print('Running migrations for database creation (version $version)');
    
    final files = _getMigrationFiles();
    
    for (final filename in files) {
      final fileVersion = _extractVersionFromFilename(filename);
      
      // Only run migrations up to the target version
      if (fileVersion <= version) {
        print('Executing migration: $filename');
        final sql = _readMigrationFile(filename);
        await _executeSqlStatements(db, sql, filename);
      }
    }
    
    print('Database creation migrations completed');
  }
  
  /// Execute migrations for database upgrade (onUpgrade)
  static Future<void> runMigrationsOnUpgrade(
      Database db, int oldVersion, int newVersion) async {
    print('Running migrations for database upgrade: $oldVersion -> $newVersion');
    
    final files = _getMigrationFiles();
    
    for (final filename in files) {
      final fileVersion = _extractVersionFromFilename(filename);
      
      // Only run migrations that are newer than old version and up to new version
      if (fileVersion > oldVersion && fileVersion <= newVersion) {
        print('Executing migration: $filename');
        final sql = _readMigrationFile(filename);
        await _executeSqlStatements(db, sql, filename);
      }
    }
    
    print('Database upgrade migrations completed');
  }
  
  /// Execute SQL statements from a migration file
  static Future<void> _executeSqlStatements(
      Database db, String sql, String filename) async {
    try {
      // Split SQL by semicolons and execute each statement
      final statements = sql
          .split(';')
          .map((s) => s.trim())
          .where((s) => s.isNotEmpty && !s.startsWith('--'))
          .toList();
      
      for (final statement in statements) {
        if (statement.isNotEmpty) {
          await db.execute(statement);
        }
      }
    } catch (e) {
      throw Exception('Error executing migration $filename: $e');
    }
  }
  
  /// Validate migration files (check for proper naming and syntax)
  static void validateMigrations() {
    final files = _getMigrationFiles();
    
    if (files.isEmpty) {
      throw StateError('No migration files found in $migrationsPath');
    }
    
    // Check for sequential numbering
    for (int i = 0; i < files.length; i++) {
      final expectedVersion = i + 1;
      final actualVersion = _extractVersionFromFilename(files[i]);
      
      if (actualVersion != expectedVersion) {
        throw StateError('Migration files must be numbered sequentially. '
            'Expected version $expectedVersion, found $actualVersion in ${files[i]}');
      }
    }
    
    print('Migration validation passed: ${files.length} files found');
  }
  
  /// Get list of available migrations with their versions
  static List<Map<String, dynamic>> getMigrationInfo() {
    final files = _getMigrationFiles();
    
    return files.map((filename) {
      final version = _extractVersionFromFilename(filename);
      final description = filename
          .replaceFirst(RegExp(r'^\d+_'), '')
          .replaceFirst('.sql', '')
          .replaceAll('_', ' ');
      
      return {
        'version': version,
        'filename': filename,
        'description': description,
      };
    }).toList();
  }
}
