import 'dart:async';
import 'dart:io';

import 'package:core/core.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import 'migration_manager.dart';

/// Manages the SQLite database and migrations using sqflite
class DatabaseManager {
  Database? _database;

  /// Get the database instance
  Database get database {
    if (_database == null) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  /// Initialize the database
  Future<void> initialize() async {
    if (_database != null) return;

    // Initialize sqflite for desktop platforms (Windows, Linux, macOS)
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    final directory = await getApplicationDocumentsDirectory();
    final dbPath = path.join(directory.path, Configuration.databaseName);

    try {
      // Use embedded migrations instead of file-based migrations
      final currentVersion = _getEmbeddedMigrationVersion();
      print('Database version: $currentVersion');

      _database = await openDatabase(
        dbPath,
        version: currentVersion,
        onCreate: (db, version) async {
          print('Creating database with version $version');
          await _runEmbeddedMigrationsOnCreate(db, version);
        },
        onUpgrade: (db, oldVersion, newVersion) async {
          print('Upgrading database from $oldVersion to $newVersion');
          await _runEmbeddedMigrationsOnUpgrade(db, oldVersion, newVersion);
        },
      );
      print('Database opened successfully at: $dbPath');
    } catch (e) {
      print('Error opening database at $dbPath: $e');
      rethrow;
    }
  }



  /// Insert a new event
  Future<void> insertEvent(Event event) async {
    final eventMap = event.toMap();
    await database.insert(
      'events',
      eventMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Insert multiple events in a transaction
  Future<void> insertEvents(List<Event> events) async {
    await database.transaction((txn) async {
      for (final event in events) {
        final eventMap = event.toMap();
        await txn.insert(
          'events',
          eventMap,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  /// Get events by entity
  Future<List<Event>> getEventsByEntity(
      String entityType, String entityId) async {
    final result = await database.query(
      'events',
      where: 'entity_type = ? AND entity_id = ?',
      whereArgs: [entityType, entityId],
      orderBy: 'timestamp ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Get unsynced events
  Future<List<Event>> getUnsyncedEvents() async {
    final result = await database.query(
      'events',
      where: 'synced = ?',
      whereArgs: [0],
      orderBy: 'timestamp ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Mark events as synced
  Future<void> markEventsSynced(List<String> eventIds) async {
    if (eventIds.isEmpty) return;

    await database.transaction((txn) async {
      for (final eventId in eventIds) {
        await txn.update(
          'events',
          {'synced': 1},
          where: 'id = ?',
          whereArgs: [eventId],
        );
      }
    });
  }

  /// Get events after timestamp
  Future<List<Event>> getEventsAfterTimestamp(String timestamp) async {
    final result = await database.query(
      'events',
      where: 'timestamp > ?',
      whereArgs: [timestamp],
      orderBy: 'timestamp ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Get all events (for debugging/export)
  Future<List<Event>> getAllEvents() async {
    final result = await database.query(
      'events',
      orderBy: 'timestamp ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Get database file path for sharing
  Future<String> getDatabasePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return path.join(directory.path, Configuration.databaseName);
  }

  /// Close the database
  Future<void> close() async {
    await _database?.close();
    _database = null;
  }

  /// Clear all events (for testing)
  Future<void> clearAllEvents() async {
    await database.delete('events');
  }

  /// Get event count
  Future<int> getEventCount() async {
    final result =
        await database.rawQuery('SELECT COUNT(*) as count FROM events');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  /// Get synced event count
  Future<int> getSyncedEventCount() async {
    final result = await database
        .rawQuery('SELECT COUNT(*) as count FROM events WHERE synced = 1');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  /// Get unsynced event count
  Future<int> getUnsyncedEventCount() async {
    final result = await database
        .rawQuery('SELECT COUNT(*) as count FROM events WHERE synced = 0');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  /// Delete old events (keep last N events)
  Future<void> deleteOldEvents({int keepCount = 1000}) async {
    await database.rawDelete('''
      DELETE FROM events
      WHERE id NOT IN (
        SELECT id FROM events
        ORDER BY timestamp DESC
        LIMIT ?
      )
    ''', [keepCount]);
  }

  /// Get migration information
  List<Map<String, dynamic>> getMigrationInfo() {
    return MigrationManager.getMigrationInfo();
  }

  /// Get current database version
  int getCurrentDatabaseVersion() {
    return _getEmbeddedMigrationVersion();
  }

  /// Get the current migration version from embedded migrations
  int _getEmbeddedMigrationVersion() {
    return 3; // We have 3 embedded migrations
  }

  /// Run embedded migrations on database creation
  Future<void> _runEmbeddedMigrationsOnCreate(Database db, int version) async {
    final migrations = _getEmbeddedMigrations();

    for (int i = 1; i <= version; i++) {
      if (migrations.containsKey(i)) {
        print('Executing embedded migration $i');
        await _executeSqlStatements(db, migrations[i]!, 'migration_$i');
      }
    }
  }

  /// Run embedded migrations on database upgrade
  Future<void> _runEmbeddedMigrationsOnUpgrade(Database db, int oldVersion, int newVersion) async {
    final migrations = _getEmbeddedMigrations();

    for (int i = oldVersion + 1; i <= newVersion; i++) {
      if (migrations.containsKey(i)) {
        print('Executing embedded migration $i');
        await _executeSqlStatements(db, migrations[i]!, 'migration_$i');
      }
    }
  }

  /// Get embedded migrations as a map
  Map<int, String> _getEmbeddedMigrations() {
    return {
      1: '''
        -- Initial schema migration
        -- Creates the events table with basic structure
        CREATE TABLE IF NOT EXISTS events (
          id TEXT PRIMARY KEY,
          origin TEXT NOT NULL,
          timestamp TEXT NOT NULL,
          entity_type TEXT NOT NULL,
          entity_id TEXT NOT NULL,
          event_type TEXT NOT NULL,
          payload TEXT NOT NULL,
          synced INTEGER DEFAULT 0
        );
      ''',
      2: '''
        -- Add indexes for better query performance
        -- Creates indexes on commonly queried columns
        CREATE INDEX IF NOT EXISTS idx_events_entity ON events(entity_type, entity_id);
        CREATE INDEX IF NOT EXISTS idx_events_timestamp ON events(timestamp);
        CREATE INDEX IF NOT EXISTS idx_events_synced ON events(synced);
        CREATE INDEX IF NOT EXISTS idx_events_origin ON events(origin);
      ''',
      3: '''
        -- Add metadata table for storing application metadata
        -- This table can store configuration, node information, etc.
        CREATE TABLE IF NOT EXISTS metadata (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL,
          created_at TEXT NOT NULL DEFAULT (datetime('now')),
          updated_at TEXT NOT NULL DEFAULT (datetime('now'))
        );

        -- Create trigger to automatically update updated_at timestamp
        CREATE TRIGGER IF NOT EXISTS update_metadata_timestamp
          AFTER UPDATE ON metadata
          FOR EACH ROW
        BEGIN
          UPDATE metadata SET updated_at = datetime('now') WHERE key = NEW.key;
        END;
      ''',
    };
  }

  /// Execute SQL statements from a migration
  Future<void> _executeSqlStatements(Database db, String sql, String migrationName) async {
    try {
      // Split SQL by semicolons and execute each statement
      final statements = sql
          .split(';')
          .map((s) => s.trim())
          .where((s) => s.isNotEmpty && !s.startsWith('--'))
          .toList();

      for (final statement in statements) {
        if (statement.isNotEmpty) {
          await db.execute(statement);
        }
      }
    } catch (e) {
      throw Exception('Error executing migration $migrationName: $e');
    }
  }
}
